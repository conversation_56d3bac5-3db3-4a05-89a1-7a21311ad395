using UnityEngine;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 序列播放控制器 - 处理播放状态和循环逻辑
    /// </summary>
    internal class SequenceController
    {
        /// <summary>
        /// 循环类型
        /// </summary>
        public enum LoopType
        {
            Restart,      // 重新开始
            Yoyo,         // 往返播放
            Incremental   // 增量循环
        }
        
        /// <summary>
        /// 播放状态
        /// </summary>
        public enum PlaybackState
        {
            Stopped,      // 停止
            Playing,      // 播放中
            Paused,       // 暂停
            Completed     // 完成
        }
        
        // 播放控制
        private PlaybackState state = PlaybackState.Stopped;  // 当前状态
        private float elapsedTime = 0f;                       // 已经过时间
        private float timeScale = 1f;                         // 时间缩放
        private bool isReversed = false;                      // 是否反向播放
        
        // 循环控制
        private int loops = 1;                                // 循环次数（-1为无限循环）
        private LoopType loopType = LoopType.Restart;       // 循环类型
        private int completedLoops = 0;                       // 已完成循环次数
        
        // 属性
        public PlaybackState State => state;
        public float ElapsedTime => elapsedTime;
        public float TimeScale => timeScale;
        public bool IsReversed => isReversed;
        public int Loops => loops;
        public LoopType LoopMode => loopType;
        public int CompletedLoops => completedLoops;
        
        /// <summary>
        /// 设置循环
        /// </summary>
        public void SetLoops(int loopCount, LoopType type)
        {
            loops = loopCount;
            loopType = type;
        }
        
        /// <summary>
        /// 设置时间缩放
        /// </summary>
        public void SetTimeScale(float scale)
        {
            timeScale = Mathf.Max(0f, scale);
        }
        
        /// <summary>
        /// 开始播放
        /// </summary>
        public void Play()
        {
            state = PlaybackState.Playing;
            elapsedTime = 0f;
            completedLoops = 0;
            isReversed = false;
        }
        
        /// <summary>
        /// 暂停播放
        /// </summary>
        public void Pause()
        {
            if (state == PlaybackState.Playing)
            {
                state = PlaybackState.Paused;
            }
        }
        
        /// <summary>
        /// 恢复播放
        /// </summary>
        public void Resume()
        {
            if (state == PlaybackState.Paused)
            {
                state = PlaybackState.Playing;
            }
        }
        
        /// <summary>
        /// 停止播放
        /// </summary>
        public void Stop()
        {
            state = PlaybackState.Stopped;
            elapsedTime = 0f;
            completedLoops = 0;
            isReversed = false;
        }
        
        /// <summary>
        /// 重新开始
        /// </summary>
        public void Restart()
        {
            elapsedTime = 0f;
            completedLoops = 0;
            isReversed = false;
            state = PlaybackState.Playing;
        }
        
        /// <summary>
        /// 跳转到指定时间
        /// </summary>
        public void Goto(float time)
        {
            elapsedTime = Mathf.Max(0f, time);
        }
        
        /// <summary>
        /// 更新时间并检查循环
        /// </summary>
        /// <returns>当前帧应该使用的时间</returns>
        public float UpdateTime(float deltaTime, float duration)
        {
            if (state != PlaybackState.Playing || duration <= 0f)
            {
                return elapsedTime;
            }
            
            // 应用时间缩放
            float scaledDelta = deltaTime * timeScale;
            
            // 更新时间
            if (isReversed)
            {
                elapsedTime -= scaledDelta;
            }
            else
            {
                elapsedTime += scaledDelta;
            }
            
            // 检查是否完成一次循环
            if (!isReversed && elapsedTime >= duration)
            {
                OnLoopComplete(duration);
            }
            else if (isReversed && elapsedTime <= 0f)
            {
                OnLoopComplete(duration);
            }
            
            // 限制时间范围
            return Mathf.Clamp(elapsedTime, 0f, duration);
        }
        
        /// <summary>
        /// 处理循环完成
        /// </summary>
        private void OnLoopComplete(float duration)
        {
            completedLoops++;
            
            // 检查是否还需要继续循环
            if (loops == -1 || completedLoops < loops)
            {
                switch (loopType)
                {
                    case LoopType.Restart:
                        elapsedTime = 0f;
                        break;
                        
                    case LoopType.Yoyo:
                        isReversed = !isReversed;
                        elapsedTime = isReversed ? duration : 0f;
                        break;
                        
                    case LoopType.Incremental:
                        // 增量循环不重置时间，继续累加
                        break;
                }
            }
            else
            {
                // 循环结束
                state = PlaybackState.Completed;
                elapsedTime = duration;
            }
        }
        
        /// <summary>
        /// 获取实际的播放时间（考虑循环类型）
        /// </summary>
        public float GetEffectiveTime(float duration)
        {
            if (loopType == LoopType.Incremental)
            {
                // 增量循环使用模运算
                return elapsedTime % duration;
            }
            
            return Mathf.Clamp(elapsedTime, 0f, duration);
        }
    }
}