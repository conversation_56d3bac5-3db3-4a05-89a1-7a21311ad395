using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 序列时间轴管理器 - 管理所有序列项的时间轴
    /// </summary>
    internal class SequenceTimeline
    {
        private List<SequenceItem> items = new List<SequenceItem>();  // 所有序列项
        private float currentInsertTime = 0f;                         // 当前插入时间点
        private float totalDuration = 0f;                              // 总持续时间
        
        /// <summary>
        /// 获取序列项列表
        /// </summary>
        public List<SequenceItem> Items => items;
        
        /// <summary>
        /// 获取总持续时间
        /// </summary>
        public float TotalDuration => totalDuration;
        
        /// <summary>
        /// 添加串行项（在当前时间轴末尾添加）
        /// </summary>
        public void AddSequential(SequenceItem item)
        {
            item.StartTime = currentInsertTime;
            items.Add(item);
            
            // 更新当前插入时间
            currentInsertTime = item.EndTime;
            
            // 更新总持续时间
            UpdateTotalDuration();
        }
        
        /// <summary>
        /// 添加并行项（与前一个项并行）
        /// </summary>
        public void AddParallel(SequenceItem item)
        {
            if (items.Count == 0)
            {
                AddSequential(item);
                return;
            }
            
            // 获取最后一个非并行项的开始时间
            float parallelStartTime = GetLastSequentialStartTime();
            item.StartTime = parallelStartTime;
            items.Add(item);
            
            // 如果这个并行项的结束时间超过了当前插入时间，需要更新
            if (item.EndTime > currentInsertTime)
            {
                currentInsertTime = item.EndTime;
            }
            
            // 更新总持续时间
            UpdateTotalDuration();
        }
        
        /// <summary>
        /// 在指定时间插入项
        /// </summary>
        public void InsertAt(float time, SequenceItem item)
        {
            item.StartTime = time;
            items.Add(item);
            
            // 如果插入项的结束时间超过了当前插入时间，更新它
            if (item.EndTime > currentInsertTime)
            {
                currentInsertTime = item.EndTime;
            }
            
            // 更新总持续时间
            UpdateTotalDuration();
        }
        
        /// <summary>
        /// 添加延迟
        /// </summary>
        public void AddInterval(float interval)
        {
            currentInsertTime += interval;
            UpdateTotalDuration();
        }
        
        /// <summary>
        /// 获取在指定时间应该执行的项
        /// </summary>
        public List<SequenceItem> GetItemsToExecuteAt(float time)
        {
            return items.Where(item => item.ShouldExecuteAt(time)).ToList();
        }
        
        /// <summary>
        /// 获取在指定时间应该完成的项
        /// </summary>
        public List<SequenceItem> GetItemsToCompleteAt(float time)
        {
            return items.Where(item => item.ShouldCompleteAt(time)).ToList();
        }
        
        /// <summary>
        /// 获取所有活跃的项（已开始但未完成）
        /// </summary>
        public List<SequenceItem> GetActiveItems()
        {
            return items.Where(item => item.IsStarted && !item.IsCompleted).ToList();
        }
        
        /// <summary>
        /// 重置时间轴
        /// </summary>
        public void Reset()
        {
            foreach (var item in items)
            {
                item.Reset();
            }
        }
        
        /// <summary>
        /// 清空时间轴
        /// </summary>
        public void Clear()
        {
            // 先停止所有活跃的Tween
            foreach (var item in items)
            {
                if (item.Type == SequenceItem.ItemType.Tween && 
                    item.Tween != null && 
                    item.Tween.IsActive)
                {
                    item.Tween.Stop();
                }
            }
            
            items.Clear();
            currentInsertTime = 0f;
            totalDuration = 0f;
        }
        
        /// <summary>
        /// 获取最后一个串行项的开始时间
        /// </summary>
        private float GetLastSequentialStartTime()
        {
            if (items.Count == 0) return 0f;
            
            // 从后往前找，找到第一个"串行"添加的项
            for (int i = items.Count - 1; i >= 0; i--)
            {
                var item = items[i];
                // 如果这个项的结束时间等于某个后续项的开始时间，说明它是串行的
                bool isSequential = false;
                
                for (int j = i + 1; j < items.Count; j++)
                {
                    if (Mathf.Approximately(item.EndTime, items[j].StartTime))
                    {
                        isSequential = true;
                        break;
                    }
                }
                
                if (isSequential || i == 0)
                {
                    return item.StartTime;
                }
            }
            
            return items[items.Count - 1].StartTime;
        }
        
        /// <summary>
        /// 更新总持续时间
        /// </summary>
        private void UpdateTotalDuration()
        {
            totalDuration = Mathf.Max(currentInsertTime, 
                items.Count > 0 ? items.Max(item => item.EndTime) : 0f);
        }
    }
}