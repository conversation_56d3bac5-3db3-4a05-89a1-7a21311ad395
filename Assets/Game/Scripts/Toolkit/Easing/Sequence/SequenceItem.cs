using System;
using UnityEngine.Events;

namespace UncleChenGames.Easing
{
    /// <summary>
    /// 序列项 - 表示序列中的一个动画或操作
    /// </summary>
    internal class SequenceItem
    {
        /// <summary>
        /// 序列项类型
        /// </summary>
        public enum ItemType
        {
            Tween,     // 缓动动画
            Callback,  // 回调函数
            Interval   // 延迟间隔
        }

        public ItemType Type { get; set; }                    // 项目类型
        public float StartTime { get; set; }                  // 开始时间
        public float Duration { get; set; }                   // 持续时间
        public float EndTime => StartTime + Duration;         // 结束时间
        public IEasingCoroutine Tween { get; set; }          // 缓动动画实例
        public UnityAction Callback { get; set; }             // 回调函数
        public bool IsStarted { get; set; }                   // 是否已开始
        public bool IsCompleted { get; set; }                 // 是否已完成
        public bool IsPaused { get; set; }                    // 是否暂停
        public float PausedTime { get; set; }                 // 暂停时的时间
        
        /// <summary>
        /// 重置序列项状态
        /// </summary>
        public void Reset()
        {
            IsStarted = false;
            IsCompleted = false;
            IsPaused = false;
            PausedTime = 0f;
            
            // 如果是Tween，停止它
            if (Type == ItemType.Tween && Tween != null && Tween.IsActive)
            {
                Tween.Stop();
            }
        }
        
        /// <summary>
        /// 检查该项是否应该在指定时间执行
        /// </summary>
        public bool ShouldExecuteAt(float time)
        {
            return !IsStarted && time >= StartTime;
        }
        
        /// <summary>
        /// 检查该项是否应该在指定时间完成
        /// </summary>
        public bool ShouldCompleteAt(float time)
        {
            return IsStarted && !IsCompleted && time >= EndTime;
        }
    }
}